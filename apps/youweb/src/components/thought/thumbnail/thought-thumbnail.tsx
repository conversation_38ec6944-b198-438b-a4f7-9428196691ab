import type { ThoughtVO } from '@repo/common/types/thought/types';
import {
  convertBase64ToProseMirrorNode,
  DIFF_CHANGE_TYPE,
  diffTransformUtils,
} from '@repo/editor-common';
import { getText, type Range } from '@tiptap/core';
import type { Node } from '@tiptap/pm/model';
import { useEffect, useRef, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TimeSince } from '@/utils/timeSince';
import { cn } from '@/utils/utils';

import './thought-thumbnail.css';

// @see https://github.com/ueberdosis/tiptap/blob/develop/packages/core/src/helpers/getTextBetween.ts
function getTextBetween(
  startNode: Node,
  range: Range,
): {
  type: string;
  text: string;
  isHeading: boolean;
}[] {
  const { from, to } = range;
  const result: {
    type: string;
    text: string;
    isHeading: boolean;
  }[] = [];

  let isHeading = false;

  startNode.nodesBetween(from, to, (node, pos, parent, index) => {
    if (node.isBlock && pos > from) {
      isHeading = false;
      if (result[result.length - 1]?.type !== 'block') {
        result.push({
          type: 'block',
          text: '',
          isHeading,
        });
      }
    }

    if (node.type.name === 'heading') {
      isHeading = true;
    }

    if (node.isText) {
      if (result[result.length - 1]?.type !== 'text') {
        result.push({
          type: 'text',
          text: '',
          isHeading,
        });
      }

      result[result.length - 1].text +=
        node?.text?.slice(Math.max(from, pos) - pos, to - pos) ?? '';
    }
  });

  return result;
}

export const getThoughtThumbnailImageUrl = (src: string) => {
  // 如果图片 URL 以 /files 开头，转换为 CDN 格式
  if (src?.startsWith('/files/')) {
    const hash = src.replace('/files/', '');
    // 返回CDN格式的@small URL
    return `https://cdn.gooo.ai/user-files/${hash}@small`;
  }

  return src;
};

export function findFirstImageNode(doc: Node): Node | null {
  let firstImageNode: Node | null = null;

  // Iterate through all nodes in the document
  doc.nodesBetween(0, doc.content.size, (node, pos) => {
    // Check if the current node is an image node
    if (node.type.name === 'image') {
      firstImageNode = node;
      return false; // Stop the iteration
    }
  });

  return firstImageNode;
}

export default function ThoughtThumbnail({
  thought,
  className,
}: {
  thought: ThoughtVO;
  className?: string;
}) {
  const tempNode = convertBase64ToProseMirrorNode(thought.content?.raw);
  const newNode = diffTransformUtils.extractContent(tempNode, DIFF_CHANGE_TYPE.ADDED);
  const range = {
    from: 0,
    to: newNode.content.size,
  };
  const lines = getTextBetween(newNode, range).slice(0, 15);
  const imageNode = findFirstImageNode(newNode);

  return (
    <Card
      className={cn(
        'thought-thumbnail-thought-container relative overflow-hidden rounded-2xl border-none shadow-md transition-shadow hover:shadow-lg',
        className,
      )}
    >
      <CardContent className="px-4 pb-4 pt-4">
        <div className="mb-[10px] border-b border-muted pb-2">
          <span
            className={cn(
              'title line-clamp-2 break-all',
              thought.title ? 'text-foreground' : 'text-disabled-foreground',
            )}
          >
            {thought.title || 'New thought'}
          </span>
        </div>
        <div className="footnote flex max-h-[234px] min-h-[54px] flex-col overflow-hidden">
          <div className={cn(imageNode ? 'line-clamp-5' : 'line-clamp-[13]')}>
            {lines
              .filter((line) => line.type === 'text')
              .map((line, index) => (
                <p
                  key={index}
                  className={cn(
                    'relative text-muted-foreground',
                    line.isHeading ? 'font-bold' : '',
                  )}
                >
                  {line.text}
                </p>
              ))}
          </div>
          {imageNode && (
            <img
              // 后面图片要加一些工具函数帮助进行尺寸转换
              src={getThoughtThumbnailImageUrl(imageNode.attrs.src)}
              alt="Thought Image"
              className="my-1 h-[128px] w-full rounded-lg object-cover object-center"
            />
          )}
        </div>

        <div className="mt-2 flex flex-row text-caption">
          <div className="flex-1 overflow-hidden">
            <div className="flex text-xs">
              <TimeSince dateString={thought.updated_at} />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export const ThoughtSmallThumbnail = ({ thought }: { thought: ThoughtVO }) => {
  const node = convertBase64ToProseMirrorNode(thought.content?.raw);
  const text = getText(node);
  const imageNode = findFirstImageNode(node);

  const thoughtContentRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    if (thoughtContentRef.current) {
      const element = thoughtContentRef.current;
      setIsOverflowing(element.scrollHeight > element.clientHeight);
    }
  }, [thoughtContentRef]);

  return (
    <Card className="relative flex h-full flex-col overflow-hidden rounded-2xl border-muted bg-card shadow-sm transition-shadow hover:shadow-lg">
      <CardContent className="flex h-full flex-col justify-between p-2">
        <div className="">
          <span
            className={cn(
              'body-strong line-clamp-2 break-all',
              thought.title ? 'text-foreground' : 'text-disabled-foreground',
            )}
          >
            {thought?.title || 'New thought'}
          </span>
          {!imageNode && (
            <p
              className="caption relative mt-[6px] line-clamp-5 text-muted-foreground"
              ref={thoughtContentRef}
            >
              {text}
              {isOverflowing && (
                <div className="pointer-events-none absolute bottom-0 h-10 w-full bg-gradient-to-b from-transparent to-card"></div>
              )}
            </p>
          )}
          {imageNode && (
            <img
              src={getThoughtThumbnailImageUrl(imageNode.attrs.src)}
              alt="Thought Image"
              className="mt-2 h-[72px] w-full rounded-md object-cover object-center"
            />
          )}
        </div>

        <div className="flex flex-row text-[10px] text-caption">
          <TimeSince dateString={thought.updated_at} />
        </div>
      </CardContent>
    </Card>
  );
};
